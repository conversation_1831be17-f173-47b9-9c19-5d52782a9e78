from pydantic import BaseModel, HttpUrl, Field
from typing import List, Optional

"""
This file defines the Pydantic models for the MCP Server's API.
These models serve as the data contract between the MCP server and its clients (e.g., the Orchestrator),
ensuring type safety and clear request/response structures.
"""

# --- API Request Models ---

class SearchRequest(BaseModel):
    """Request body for the /v1/search endpoint."""
    topic: str = Field(..., description="The search term or topic to query on Amazon.")
    limit: int = Field(10, gt=0, le=50, description="The maximum number of product ASINs to return.")

class ProductDetailsRequest(BaseModel):
    """Request body for the /v1/product-details endpoint."""
    asin: str = Field(..., description="The Amazon Standard Identification Number (ASIN) of the product.")

class FetchImageRequest(BaseModel):
    """Request body for the /v1/fetch-image endpoint."""
    image_url: HttpUrl = Field(..., description="The public URL of the image to fetch.")


# --- API Response Models ---

class ProductIdentifier(BaseModel):
    """A container for a single product's ASIN."""
    asin: str

class SearchResponseData(BaseModel):
    """The 'data' payload for a successful search response."""
    search_term: str
    product_identifiers: List[ProductIdentifier]

class ProductDetailsData(BaseModel):
    """The 'data' payload for a successful product details response."""
    asin: str
    title: str
    description: Optional[str] = None
    content: Optional[str] = None
    price: Optional[float] = None
    currency: Optional[str] = None
    main_image_url: Optional[HttpUrl] = None
    secondary_image_urls: List[HttpUrl] = []