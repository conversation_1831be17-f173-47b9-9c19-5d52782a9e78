version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: mcp-postgres
    environment:
      POSTGRES_DB: mcp_server
      POSTGRES_USER: mcp_user
      POSTGRES_PASSWORD: mcp_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U mcp_user -d mcp_server"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MCP Server Application
  mcp-server:
    build:
      context: .
      target: production
    container_name: mcp-server
    environment:
      NODE_ENV: production
      DATABASE_URL: ************************************************/mcp_server?schema=public
      PORT: 3000
      LOG_LEVEL: info
      LOG_FORMAT: json
      PROXY_ROTATION_ENABLED: false
      USER_AGENT_ROTATION_ENABLED: true
      RATE_LIMIT_WINDOW_MS: 900000
      RATE_LIMIT_MAX_REQUESTS: 100
      MAX_CONTENT_TOKENS: 4000
      CONTENT_TRUNCATION_ENABLED: true
    ports:
      - "3000:3000"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Development version with hot reload
  mcp-server-dev:
    build:
      context: .
      target: development
    container_name: mcp-server-dev
    environment:
      NODE_ENV: development
      DATABASE_URL: ************************************************/mcp_server?schema=public
      PORT: 3000
      LOG_LEVEL: debug
      LOG_FORMAT: human
      PROXY_ROTATION_ENABLED: false
      USER_AGENT_ROTATION_ENABLED: true
    ports:
      - "3001:3000"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/node_modules
    profiles:
      - dev

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    container_name: mcp-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    profiles:
      - cache

volumes:
  postgres_data:
  redis_data:
