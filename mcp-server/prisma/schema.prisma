// prisma/schema.prisma

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model Product {
  id                String    @id @default(cuid())
  asin              String    @unique
  title             String?
  description       String?
  content           String?
  price             Float?
  currency          String?
  mainImageUrl      String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  searchQueries     SearchQuery[]
}

model SearchQuery {
  id        String    @id @default(cuid())
  topic     String
  limit     Int       @default(10)
  createdAt DateTime  @default(now())
  products  Product[]
}

model Proxy {
  id            String   @id @default(cuid())
  url           String   @unique
  isActive      Boolean  @default(true)
  lastUsed      DateTime?
  failureCount  Int      @default(0)
  createdAt     DateTime @default(now())
}

model ScrapingLog {
  id          String    @id @default(cuid())
  asin        String?
  topic       String?
  statusCode  Int
  error       String?
  userAgent   String
  proxyUrl    String?
  createdAt   DateTime  @default(now())
}