import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';

// Zod schemas for request validation
export const searchRequestSchema = z.object({
  topic: z.string().min(1, 'Topic is required').max(200, 'Topic must be less than 200 characters'),
  limit: z.number().int().min(1).max(50).optional().default(10)
});

export const productDetailsRequestSchema = z.object({
  asin: z.string().min(10, 'ASIN must be at least 10 characters').max(10, 'ASIN must be exactly 10 characters').regex(/^[A-Z0-9]+$/, 'ASIN must contain only uppercase letters and numbers')
});

export const fetchImageRequestSchema = z.object({
  image_url: z.string().url('Must be a valid URL').refine(
    (url) => {
      try {
        const parsedUrl = new URL(url);
        return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
      } catch {
        return false;
      }
    },
    'Must be a valid HTTP or HTTPS URL'
  )
});

// Generic validation middleware factory
export const validateRequest = (schema: z.ZodSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      // Validate request body
      const validatedData = schema.parse(req.body);
      
      // Replace request body with validated data
      req.body = validatedData;
      
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Format Zod validation errors
        const formattedErrors = error.issues.map((err: any) => ({
          field: err.path.join('.'),
          message: err.message,
          received: err.received
        }));
        
        res.status(400).json({
          error: 'Validation failed',
          details: formattedErrors
        });
      } else {
        // Handle unexpected errors
        console.error('Unexpected validation error:', error);
        res.status(500).json({
          error: 'Internal server error during validation'
        });
      }
    }
  };
};

// Specific validation middleware for each endpoint
export const validateSearchRequest = validateRequest(searchRequestSchema);
export const validateProductDetailsRequest = validateRequest(productDetailsRequestSchema);
export const validateFetchImageRequest = validateRequest(fetchImageRequestSchema);
