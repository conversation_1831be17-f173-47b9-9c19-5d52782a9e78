import { Router } from 'express';
import productDetailsController from '../controllers/product-details';
import { validateProductDetailsRequest } from '../middleware/validation';

const router = Router();

/**
 * POST /product-details
 * Fetches and parses detailed information for a single product using its ASIN
 */
router.post('/', validateProductDetailsRequest, productDetailsController.getProductDetails);

export default router;