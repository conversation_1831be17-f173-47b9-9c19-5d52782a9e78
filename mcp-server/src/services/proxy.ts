import prisma from '../lib/prisma';

/**
 * Proxy rotation service for avoiding IP bans
 */

interface ProxyConfig {
  url: string;
  isActive: boolean;
  lastUsed?: Date;
  failureCount: number;
}

class ProxyService {
  private proxies: ProxyConfig[] = [];
  private currentIndex: number = 0;
  private maxFailures: number = 3;
  private cooldownPeriod: number = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.loadProxiesFromDatabase();
  }

  /**
   * Load proxies from database
   */
  private async loadProxiesFromDatabase(): Promise<void> {
    try {
      const dbProxies = await prisma.proxy.findMany({
        where: { isActive: true }
      });

      this.proxies = dbProxies.map(proxy => ({
        url: proxy.url,
        isActive: proxy.isActive,
        lastUsed: proxy.lastUsed || undefined,
        failureCount: proxy.failureCount
      }));

      console.log(`Loaded ${this.proxies.length} proxies from database`);
    } catch (error) {
      console.error('Error loading proxies from database:', error);
      this.proxies = [];
    }
  }

  /**
   * Add a new proxy to the rotation
   */
  async addProxy(proxyUrl: string): Promise<void> {
    try {
      // Validate proxy URL format
      new URL(proxyUrl);

      // Add to database
      await prisma.proxy.create({
        data: {
          url: proxyUrl,
          isActive: true,
          failureCount: 0
        }
      });

      // Add to local cache
      this.proxies.push({
        url: proxyUrl,
        isActive: true,
        failureCount: 0
      });

      console.log(`Added new proxy: ${proxyUrl}`);
    } catch (error) {
      console.error('Error adding proxy:', error);
      throw new Error('Invalid proxy URL or database error');
    }
  }

  /**
   * Get the next available proxy
   */
  async getNextProxy(): Promise<string | null> {
    if (this.proxies.length === 0) {
      await this.loadProxiesFromDatabase();
      if (this.proxies.length === 0) {
        return null;
      }
    }

    // Filter out failed proxies and those in cooldown
    const availableProxies = this.proxies.filter(proxy => 
      proxy.isActive && 
      proxy.failureCount < this.maxFailures &&
      (!proxy.lastUsed || Date.now() - proxy.lastUsed.getTime() > this.cooldownPeriod)
    );

    if (availableProxies.length === 0) {
      console.warn('No available proxies found');
      return null;
    }

    // Get next proxy in rotation
    const proxy = availableProxies[this.currentIndex % availableProxies.length];
    this.currentIndex = (this.currentIndex + 1) % availableProxies.length;

    // Update last used time
    await this.updateProxyUsage(proxy.url);

    return proxy.url;
  }

  /**
   * Report a proxy failure
   */
  async reportProxyFailure(proxyUrl: string): Promise<void> {
    try {
      // Update failure count in database
      await prisma.proxy.update({
        where: { url: proxyUrl },
        data: {
          failureCount: { increment: 1 }
        }
      });

      // Update local cache
      const proxy = this.proxies.find(p => p.url === proxyUrl);
      if (proxy) {
        proxy.failureCount++;
        
        // Deactivate if too many failures
        if (proxy.failureCount >= this.maxFailures) {
          proxy.isActive = false;
          await prisma.proxy.update({
            where: { url: proxyUrl },
            data: { isActive: false }
          });
          console.warn(`Deactivated proxy due to failures: ${proxyUrl}`);
        }
      }
    } catch (error) {
      console.error('Error reporting proxy failure:', error);
    }
  }

  /**
   * Update proxy usage timestamp
   */
  private async updateProxyUsage(proxyUrl: string): Promise<void> {
    try {
      await prisma.proxy.update({
        where: { url: proxyUrl },
        data: { lastUsed: new Date() }
      });

      // Update local cache
      const proxy = this.proxies.find(p => p.url === proxyUrl);
      if (proxy) {
        proxy.lastUsed = new Date();
      }
    } catch (error) {
      console.error('Error updating proxy usage:', error);
    }
  }

  /**
   * Get proxy configuration for axios
   */
  async getProxyConfig(): Promise<any | null> {
    const proxyUrl = await this.getNextProxy();
    
    if (!proxyUrl) {
      return null;
    }

    try {
      const url = new URL(proxyUrl);
      return {
        host: url.hostname,
        port: parseInt(url.port) || (url.protocol === 'https:' ? 443 : 80),
        protocol: url.protocol.replace(':', ''),
        auth: url.username && url.password ? {
          username: url.username,
          password: url.password
        } : undefined
      };
    } catch (error) {
      console.error('Error parsing proxy URL:', error);
      return null;
    }
  }

  /**
   * Check if proxy rotation is enabled
   */
  isEnabled(): boolean {
    return process.env.PROXY_ROTATION_ENABLED === 'true' && this.proxies.length > 0;
  }

  /**
   * Get proxy statistics
   */
  getStats(): { total: number; active: number; failed: number } {
    const total = this.proxies.length;
    const active = this.proxies.filter(p => p.isActive && p.failureCount < this.maxFailures).length;
    const failed = this.proxies.filter(p => !p.isActive || p.failureCount >= this.maxFailures).length;

    return { total, active, failed };
  }
}

// Export singleton instance
export const proxyService = new ProxyService();
export default proxyService;
