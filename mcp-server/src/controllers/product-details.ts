import { Request, Response }  from 'express';
import axios from 'axios';
import cheerio from 'cheerio';
import prisma from '../lib/prisma';
import { ProductDetailsRequest, ProductDetails, ApiResponse }  from '../types';
import userAgentService from '../services/user-agent';
import proxyService from '../services/proxy';

// Base URL for Amazon product pages
const AMAZON_BASE_URL = 'https://www.amazon.sg';

/**
 * Controller function to get detailed product information from Amazon.sg
 * @param req - Express request object containing the ASIN
 * @param res - Express response object
 */
export const getProductDetails = async (req: Request, res: Response): Promise<void> => {
  try {
    // Extract ASIN from request body
    const { asin }: ProductDetailsRequest = req.body;
    
    // Validate required parameter
    if (!asin) {
      res.status(400).json({
        error: 'ASIN is required for product details extraction'
      });
      return;
    }
    
    // Create product URL
    const productUrl = `${AMAZON_BASE_URL}/dp/${asin}`;

    // Get user agent and proxy configuration
    const userAgent = userAgentService.getRotatedUserAgent();
    const proxyConfig = proxyService.isEnabled() ? await proxyService.getProxyConfig() : null;

    // Make request to Amazon product page
    const axiosConfig: any = {
      headers: {
        'User-Agent': userAgent
      },
      timeout: 30000
    };

    if (proxyConfig) {
      axiosConfig.proxy = proxyConfig;
    }

    const response = await axios.get(productUrl, axiosConfig);

    // Parse HTML with Cheerio
    const $ = cheerio.load(response.data as string);
    
    // Try to extract data from JSON-LD structured data first (preferred method)
    let title: string | null = null;
    let description: string | null = null;
    let price: number | null = null;
    let currency: string | null = null;
    let mainImageUrl: string | null = null;
    
    // Look for JSON-LD script tags containing product data
    const jsonLdScripts = $('script[type="application/ld+json"]');
    let jsonLdData: any = null;
    
    jsonLdScripts.each((_index, element) => {
      try {
        const scriptContent = $(element).html();
        if (scriptContent) {
          const parsed = JSON.parse(scriptContent);
          // Look for product data in the structured data
          if (parsed['@type'] === 'Product' || (Array.isArray(parsed) && parsed.some((item: any) => item['@type'] === 'Product'))) {
            const productData = Array.isArray(parsed) ? parsed.find((item: any) => item['@type'] === 'Product') : parsed;
            jsonLdData = productData;
            return false; // Break out of each loop
          }
        }
      } catch (parseError) {
        console.error('Error parsing JSON-LD script:', parseError);
      }
      return true; // Continue iteration
    });
    
    // Extract data from JSON-LD if available
    if (jsonLdData) {
      title = jsonLdData.name || null;
      description = jsonLdData.description || null;
      
      // Extract price and currency from offers
      if (jsonLdData.offers) {
        const offers = Array.isArray(jsonLdData.offers) ? jsonLdData.offers : [jsonLdData.offers];
        const validOffer = offers.find((offer: any) => 
          offer.availability === 'https://schema.org/InStock' || 
          offer.availability === 'InStock'
        );
        
        if (validOffer) {
          price = parseFloat(validOffer.price);
          currency = validOffer.priceCurrency || null;
        }
      }
      
      // Extract main image URL
      if (jsonLdData.image) {
        mainImageUrl = Array.isArray(jsonLdData.image) ? jsonLdData.image[0] : jsonLdData.image;
      }
    }
    
    // Fallback to HTML scraping if structured data is incomplete
    if (!title) {
      const titleElement = $('span#productTitle, #title, #titleSection h1');
      title = titleElement.length > 0 ? titleElement.text().trim() : null;
    }
    
    if (!description) {
      const descriptionElement = $('#productDescription, #aplus, #feature-bullets, #productOverview');
      description = descriptionElement.length > 0 ? descriptionElement.text().trim() : null;
    }
    
    // Extract price if not found in structured data
    if (!price || !currency) {
      const priceElement = $('.a-price[data-a-size=xl], .a-price-whole, #priceblock_ourprice, #priceblock_dealprice');
      if (priceElement.length > 0) {
        // Extract price text and parse it
        const priceText = priceElement.text().replace(/[^[0-9]\.,]/g, '');
        price = parseFloat(priceText.replace(',', ''));
        
        // Determine currency from price element or page
        if (priceElement.length > 0) {
          const priceHtml = priceElement.html() || '';
          if (priceHtml.includes('S$') || priceHtml.includes('SGD')) {
            currency = 'SGD';
          } else if (priceHtml.includes('$') || priceHtml.includes('USD')) {
            currency = 'USD';
          }
        }
      }
    }
    
    // Extract main image URL if not found in structured data
    if (!mainImageUrl) {
      const imgElement = $('#img-main, #landingImage, #imgTagWrapperId img, .a-dynamic-image');
      if (imgElement.length > 0) {
        mainImageUrl = imgElement.attr('src') || imgElement.attr('data-old-hires') || imgElement.attr('data-a-dynamic-image') || null;
        
        // If data-a-dynamic-image has multiple URLs, parse the JSON to get the largest one
        if (mainImageUrl && mainImageUrl.includes('{')) {
          try {
            const imageMap = JSON.parse(mainImageUrl.replace(/&quot;/g, '"'));
            const sortedUrls = Object.keys(imageMap).sort((a, b) => {
              const sizeA = imageMap[a];
              const sizeB = imageMap[b];
              return sizeB[0] * sizeB[1] - sizeA[0] * sizeA[1]; // Sort by area
            });
            mainImageUrl = sortedUrls[0];
          } catch (jsonError) {
            console.error('Error parsing dynamic image data:', jsonError);
          }
        }
      }
    }
    
    // Extract secondary image URLs
    const secondaryImageUrls: string[] = [];
    $('#altImages img, .imageThumbnail img').each((_index, element) => {
      const imgUrl = $(element).attr('src') || $(element).attr('data-old-hires');
      if (imgUrl && !secondaryImageUrls.includes(imgUrl)) {
        secondaryImageUrls.push(imgUrl);
      }
    });
    
    // Extract and truncate content (implementation details)
    // For token optimization, we'll extract relevant text sections
    let content = '';
    
    // Extract from various content sections
    const contentSelectors = [
      '#productDescription',
      '#aplus',
      '#feature-bullets',
      '#productOverview',
      '#productDetails',
      '.poTabular' // Product overview table
    ];
    
    contentSelectors.forEach(selector => {
      const element = $(selector);
      if (element.length > 0) {
        content += element.text().trim() + ' ';
      }
    });
    
    // Basic cleaning of content
    content = content.replace(/\s+/g, ' ').trim();
    
    // Limit content to approximately 4000 tokens (rough estimate, assuming ~4 chars per token)
    // This provides a margin while staying under the limit
    if (content.length > 15000) {
      content = content.substring(0, 15000);
    }
    
    // Create product details response
    const productDetails: ProductDetails = {
      asin,
      title: title || '',
      description: description || undefined,
      content,
      price: price || 0,
      currency: currency || 'SGD',
      main_image_url: mainImageUrl || '',
      secondary_image_urls: secondaryImageUrls
    };
    
    // Save product to database
    await prisma.product.upsert({
      where: { asin },
      update: {
        title,
        description,
        content,
        price,
        currency,
        mainImageUrl
      },
      create: {
        asin,
        title,
        description,
        content,
        price,
        currency,
        mainImageUrl
      }
    });
    
    // Create API response
    const apiResponse: ApiResponse<ProductDetails> = {
      data: productDetails
    };

    res.json(apiResponse);

  } catch (error: any) {
    console.error('Error fetching product details:', error);
    
    // Handle specific error cases
    if (error.response && error.response.status === 404) {
      res.status(404).json({
        error: 'Product not found'
      });
      return;
    } else if (error.response && error.response.status === 429) {
      res.status(429).json({
        error: 'Too many requests. Please try again later'
      });
      return;
    } else if (error.code === 'ECONNREFUSED') {
      res.status(503).json({
        error: 'Unable to connect to Amazon. Service may be unavailable'
      });
      return;
    }
    
    // Generic error response
    res.status(500).json({
      error: 'Internal server error while fetching product details'
    });
  }
};

export default { getProductDetails };