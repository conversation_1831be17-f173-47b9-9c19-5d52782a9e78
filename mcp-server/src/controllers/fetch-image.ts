import { Request, Response } from 'express';
import axios from 'axios';
import { FetchImageRequest } from '../types';
import userAgentService from '../services/user-agent';
import proxyService from '../services/proxy';

/**
 * Controller function to fetch image binary data from a URL
 * @param req - Express request object containing the image URL
 * @param res - Express response object
 */
export const fetchImage = async (req: Request, res: Response): Promise<void> => {
  try {
    // Extract image URL from request body
    const { image_url }: FetchImageRequest = req.body;
    
    // Validate required parameter
    if (!image_url) {
      res.status(400).json({
        error: 'image_url is required for image fetching'
      });
      return;
    }
    
    // Validate URL format
    try {
      new URL(image_url);
    } catch (urlError) {
      res.status(400).json({
        error: 'Invalid image_url format'
      });
      return;
    }
    
    // Get user agent and proxy configuration
    const userAgent = userAgentService.getRotatedUserAgent();
    const proxyConfig = proxyService.isEnabled() ? await proxyService.getProxyConfig() : null;

    // Make request to fetch the image
    const axiosConfig: any = {
      responseType: 'stream',
      headers: {
        'User-Agent': userAgent
      },
      timeout: 30000 // 30 second timeout
    };

    if (proxyConfig) {
      axiosConfig.proxy = proxyConfig;
    }

    const response = await axios.get(image_url, axiosConfig);
    
    // Get content type from response headers
    const contentType = response.headers['content-type'] || 'application/octet-stream';
    
    // Validate that it's an image
    if (!contentType.startsWith('image/')) {
      res.status(400).json({
        error: 'URL does not point to an image resource'
      });
      return;
    }
    
    // Set appropriate headers
    res.setHeader('Content-Type', contentType);
    
    // Get content length if available
    if (response.headers['content-length']) {
      res.setHeader('Content-Length', response.headers['content-length']);
    }
    
    // Pipe the image data to the response
    (response.data as any).pipe(res);
    
  } catch (error: any) {
    console.error('Error fetching image:', error);
    
    // Handle specific error cases
    if (error.response && error.response.status === 404) {
      res.status(404).json({
        error: 'Image not found at the specified URL'
      });
      return;
    } else if (error.response && error.response.status === 403) {
      res.status(403).json({
        error: 'Access denied to the image resource'
      });
      return;
    } else if (error.code === 'ECONNREFUSED') {
      res.status(503).json({
        error: 'Unable to connect to the image server'
      });
      return;
    } else if (error.code === 'ENOTFOUND') {
      res.status(404).json({
        error: 'Image server not found'
      });
      return;
    } else if (error.code === 'ETIMEDOUT') {
      res.status(408).json({
        error: 'Request timeout while fetching image'
      });
      return;
    }
    
    // Generic error response
    res.status(500).json({
      error: 'Internal server error while fetching image'
    });
  }
};

export default { fetchImage };
