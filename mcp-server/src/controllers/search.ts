import { Request, Response } from 'express';
import axios from 'axios';
import cheerio from 'cheerio';
import prisma from '../lib/prisma';
import { SearchRequest, SearchResponse, ApiResponse } from '../types';
import userAgentService from '../services/user-agent';
import proxyService from '../services/proxy';
import logger from '../services/logger';

// Base URL for Amazon.sg
const AMAZON_BASE_URL = 'https://www.amazon.sg';

/**
 * Controller function to search for products on Amazon.sg
 * @param req - Express request object containing the search topic and limit
 * @param res - Express response object
 */
export const searchProducts = async (req: Request, res: Response): Promise<void> => {
  const startTime = Date.now();

  try {
    // Extract parameters from request body
    const { topic, limit = 10 }: SearchRequest = req.body;

    logger.logScrapingAttempt({
      topic,
      url: `${AMAZON_BASE_URL}/s?k=${encodeURIComponent(topic)}`
    });

    // Validate required parameter
    if (!topic) {
      logger.logValidationError('/search', [{ field: 'topic', message: 'Topic is required' }]);
      res.status(400).json({
        error: 'Topic is required for product search'
      });
      return;
    }
    
    // Create search URL
    const searchUrl = `${AMAZON_BASE_URL}/s?k=${encodeURIComponent(topic)}`;

    // Get user agent and proxy configuration
    const userAgent = userAgentService.getRotatedUserAgent();
    const proxyConfig = proxyService.isEnabled() ? await proxyService.getProxyConfig() : null;

    // Make request to Amazon.sg
    const axiosConfig: any = {
      headers: {
        'User-Agent': userAgent
      },
      timeout: 30000
    };

    if (proxyConfig) {
      axiosConfig.proxy = proxyConfig;
    }

    const response = await axios.get<string>(searchUrl, axiosConfig);
    const responseTime = Date.now() - startTime;

    logger.logScrapingSuccess({
      topic,
      url: searchUrl,
      statusCode: response.status,
      responseTime,
      userAgent,
      proxyUrl: proxyConfig?.host
    });

    // Parse HTML with Cheerio
    const $ = cheerio.load(response.data);

    // Extract product ASINs from the page
    // Amazon typically stores ASIN in data-asin attributes on result elements
    const productIdentifiers: { asin: string }[] = [];

    // Look for elements with data-asin attribute (common pattern on Amazon)
    $('[data-asin]').each((_index, element) => {
      const asin = $(element).attr('data-asin');
      
      // Only add valid ASINs and respect the limit
      if (asin && asin.trim() !== '' && productIdentifiers.length < limit) {
        productIdentifiers.push({ asin });
      }
    });
    
    // Create response object
    const searchResponse: SearchResponse = {
      search_term: topic,
      product_identifiers: productIdentifiers
    };
    
    // Save search query to database
    // First, ensure products exist in the database
    const productPromises = productIdentifiers.map(({ asin }) =>
      prisma.product.upsert({
        where: { asin },
        update: {},
        create: { asin }
      })
    );

    await Promise.all(productPromises);

    // Create search query and link to products
    await prisma.searchQuery.create({
      data: {
        topic,
        limit,
        products: {
          connect: productIdentifiers.map(({ asin }) => ({ asin }))
        }
      }
    });
    
    // Return successful response
    const apiResponse: ApiResponse<SearchResponse> = {
      data: searchResponse
    };
    
    logger.info('Search completed successfully', {
      topic,
      productCount: productIdentifiers.length,
      responseTime: Date.now() - startTime
    });

    res.json(apiResponse);

  } catch (error: any) {
    const responseTime = Date.now() - startTime;

    logger.logScrapingFailure({
      topic: req.body.topic || 'unknown',
      url: `${AMAZON_BASE_URL}/s?k=${encodeURIComponent(req.body.topic || '')}`,
      error: error.message,
      responseTime,
      statusCode: error.response?.status
    });
    
    // Handle specific error cases
    if (error.response && error.response.status === 404) {
      res.status(404).json({
        error: 'No products found for the given topic'
      });
      return;
    } else if (error.response && error.response.status === 429) {
      res.status(429).json({
        error: 'Too many requests. Please try again later'
      });
      return;
    } else if (error.code === 'ECONNREFUSED') {
      res.status(503).json({
        error: 'Unable to connect to Amazon. Service may be unavailable'
      });
      return;
    }
    
    // Generic error response
    res.status(500).json({
      error: 'Internal server error while searching for products'
    });
  }
};

export default { searchProducts };