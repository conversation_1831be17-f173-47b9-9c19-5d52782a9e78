{"compilerOptions": {"target": "es2020", "module": "commonjs", "lib": ["es2020", "dom"], "allowJs": true, "outDir": "./build", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "removeComments": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "sourceMap": true, "declaration": true}, "include": ["src/**/*"], "exclude": ["node_modules"]}