# Design Document: MCP (My Content Pipeline) Server

## 1. Overview

This document details the design for the **MCP Server**, a specialized microservice responsible for all interactions with the `amazon.sg` marketplace. Its purpose is to abstract away the complexities of web scraping and provide a clean, stable API for the main Content Generation Pipeline.

The server will handle three primary functions as required by the orchestrator:
1.  Searching for products by topic.
2.  Extracting detailed information for a specific product.
3.  Fetching binary image data from a URL.

## 2. Proposed Technology Stack

- **Language / Framework**: **TypeScript** with **Express.js** for its robust ecosystem, middleware support, and asynchronous capabilities. TypeScript will provide type safety and improve code maintainability.
- **ORM**: **Prisma** as the primary ORM for database interactions, providing type-safe database access and excellent TypeScript integration.
- **HTTP Client**: **Axios** for making async HTTP requests to `amazon.sg` with robust error handling.
- **HTML/Data Parsing**:
    - **Cheerio**: For fast server-side HTML scraping and manipulation with a jQuery-like syntax.
    - **Extruct**: Port of the Python library or similar for extracting embedded structured data (JSON-LD, Microdata).
    - **Puppeteer** (optional): For cases where JavaScript rendering is required if headless scraping fails.
- **Data Validation**: **Zod** for request validation and data schema definition, providing excellent TypeScript integration and type inference.
- **Deployment**: The application will be containerized using **Docker** for portability and easy deployment on services like Google Cloud Run, AWS ECS, or Kubernetes.

## 3. API Endpoints

The API will be versioned (e.g., `/v1/...`) to allow for future non-breaking changes.

### 3.1. Product Search

This endpoint takes a topic and returns a list of product ASINs found on Amazon.

- **Endpoint**: `POST /v1/search`
- **Description**: Searches `amazon.sg` for products based on a query string.
- **Request Body**:
  ```json
  {
    "topic": "best ergonomic office chairs",
    "limit": 10
  }
  ```
- **Success Response (`200 OK`)**:
  ```json
  {
    "data": {
      "search_term": "best ergonomic office chairs",
      "product_identifiers": [
        {"asin": "B081H3Y55N"},
        {"asin": "B07Y8B1D3C"}
      ]
    }
  }
  ```
- **Error Response (`404 Not Found`)**: Returned if no products are found for the given topic.

### 3.2. Product Details Extraction

This endpoint retrieves all required information for a single product, implementing the specified token optimization logic.

- **Endpoint**: `POST /v1/product-details`
- **Description**: Fetches and parses detailed information for a single product using its ASIN.
- **Request Body**:
  ```json
  {
    "asin": "B081H3Y55N"
  }
  ```
- **Success Response (`200 OK`)**:
  ```json
  {
    "data": {
      "asin": "B081H3Y55N",
      "title": "Herman Miller Aeron Chair",
      "description": "...",
      "content": "...",
      "price": 1599.00,
      "currency": "SGD",
      "main_image_url": "https://m.media-amazon.com/images/I/image1.jpg",
      "secondary_image_urls": [
        "https://m.media-amazon.com/images/I/image2.jpg"
      ]
    }
  }
  ```
- **Implementation Notes**:
  1.  Fetches the product page `https://www.amazon.sg/dp/{asin}`.
  2.  **Structured Data First**: Attempts to parse all required fields from `application/ld+json` data embedded in the page's HTML.
  3.  **Fallback to Scraping**: If structured data is missing or incomplete, it falls back to parsing the HTML using CSS selectors.
  4.  **Content Truncation**: The `content` field will be populated by scraping relevant text sections and truncating the result to the first 4000 tokens to manage costs, as specified in the requirements.

### 3.3. Image Fetching

This endpoint acts as a proxy to download an image, which the orchestrator can then stream directly to S3.

- **Endpoint**: `POST /v1/fetch-image`
- **Description**: Downloads the binary data of an image from a given URL.
- **Request Body**:
  ```json
  {
    "image_url": "https://m.media-amazon.com/images/I/image1.jpg"
  }
  ```
- **Success Response (`200 OK`)**:
  - **Headers**: `Content-Type: image/jpeg` (or other appropriate MIME type).
  - **Body**: The raw binary data of the image.

## 4. Reliability and Error Handling

- **Anti-Scraping Strategy**: To ensure long-term reliability and avoid IP bans from Amazon, the MCP server **must** be configured to use a **rotating proxy service** (e.g., Bright Data, ScraperAPI, or a private proxy pool). It will also rotate browser `User-Agent` strings on every request.
- **Error Codes**: The API will use standard HTTP status codes to signal outcomes (e.g., `200` for success, `404` for not found, `429` for rate-limited, `503` if a CAPTCHA is detected).
- **Structured Logging**: The service will produce structured (JSON) logs for all major events, including requests, errors, and when a fallback from structured data to HTML scraping occurs. This is critical for monitoring and debugging.

## 5. Database Design with Prisma ORM

The MCP server will use Prisma ORM for all database interactions, providing type-safe access and excellent TypeScript integration. The database schema will be designed to store scraped product data and maintain state for reliability.

### 5.1 Prisma Schema

The Prisma schema will define the following models:

```prisma
// schema.prisma
model Product {
  id                String    @id @default(cuid())
  asin              String    @unique
  title             String?
  description       String?
  content           String?
  price             Float?
  currency          String?
  mainImageUrl      String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  searchQueries     SearchQuery[]
}

model SearchQuery {
  id        String    @id @default(cuid())
  topic     String
  limit     Int       @default(10)
  createdAt DateTime  @default(now())
  products  Product[]
}

model Proxy {
  id            String   @id @default(cuid())
  url           String   @unique
  isActive      Boolean  @default(true)
  lastUsed      DateTime?
  failureCount  Int      @default(0)
  createdAt     DateTime @default(now())
}

model ScrapingLog {
  id          String    @id @default(cuid())
  asin        String?
  topic       String?
  statusCode  Int
  error       String?
  userAgent   String
  proxyUrl    String?
  createdAt   DateTime  @default(now())
}
```

### 5.2 Database Migrations

Database schema changes will be managed using Prisma Migrate, creating incremental migration files that can be applied in production. The migration workflow will be:

1. Update the Prisma schema based on requirements
2. Generate a new migration: `npx prisma migrate dev --name "add_product_fields"`
3. Review the generated SQL migration script
4. Apply the migration to the development database
5. Commit both the schema.prisma file and the migration script to version control
6. Apply migrations in production using: `npx prisma migrate deploy`

## 6. TypeScript Interfaces

To ensure type safety throughout the application, we'll define TypeScript interfaces that correspond to our API contracts and database models:

```typescript
// types.ts

// API Request Types
interface SearchRequest {
  topic: string;
  limit?: number;
}

interface ProductDetailsRequest {
  asin: string;
}

interface FetchImageRequest {
  image_url: string;
}

// API Response Types
interface ApiResponse<T> {
  data: T;
  error?: string;
}

interface SearchResponse {
  search_term: string;
  product_identifiers: { asin: string }[];
}

interface ProductDetails {
  asin: string;
  title: string;
  description?: string;
  content: string;
  price: number;
  currency: string;
  main_image_url: string;
  secondary_image_urls: string[];
}

// Database Model Types (generated by Prisma)
// These will be automatically generated by Prisma Client
// and provide type safety for database operations

type ProductModel = {
  id: string;
  asin: string;
  title: string | null;
  description: string | null;
  content: string | null;
  price: number | null;
  currency: string | null;
  mainImageUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
};

## 7. Testing Strategy

A comprehensive testing strategy will ensure the reliability and stability of the MCP server. The testing approach will include unit, integration, and end-to-end tests.

### 7.1 Unit Testing

Unit tests will focus on individual functions and components in isolation using Jest as the test framework:

- **HTML Parsing Functions**: Test cheerio-based parsing functions with sample HTML snippets to ensure they extract data correctly
- **Data Transformation**: Test functions that transform scraped data into the required format
- **Validation Functions**: Test Zod schemas and custom validation logic
- **Utility Functions**: Test helper functions for URL construction, string manipulation, etc.

```typescript
// Example unit test
import { parseProductTitle } from './parsers';

describe('parseProductTitle', () => {
  it('should extract title from HTML', () => {
    const html = '<span id="productTitle">Sample Product</span>';
    const $ = cheerio.load(html);
    expect(parseProductTitle($)).toBe('Sample Product');
  });
});
```

### 7.2 Integration Testing

Integration tests will verify the interaction between multiple components, particularly focusing on the API endpoints and database integration:

- **API Endpoint Testing**: Use Supertest to test API routes with mocking for external HTTP calls (to Amazon)
- **Database Integration**: Test Prisma queries and data access patterns using a test database
- **Service Layer Testing**: Test the business logic that combines data access and external API calls

The integration tests will use Jest with a test database initialized before each test suite. External HTTP calls will be mocked using tools like `nock` or `jest.mock()`.

### 7.3 End-to-End (E2E) Testing

E2E tests will validate the complete workflow of the MCP server, running against a real (but isolated) environment:

- **Full API Workflow**: Test the complete flow from API request to response, including database operations
- **Error Handling**: Verify proper error responses for various failure scenarios (network issues, invalid inputs, etc.)
- **Performance Testing**: Measure response times under different loads using tools like Artillery

E2E tests will run against a staging environment with a dedicated database and limited proxy resources to avoid unnecessary costs.

### 7.4 Testing Workflow

The testing workflow will be integrated into the development process:

1. Run unit tests on every commit with `npm test`
2. Run integration tests in CI/CD pipeline on pull requests
3. Run E2E tests nightly or on demand for release candidates
4. Generate code coverage reports with `jest --coverage`
5. Enforce minimum coverage thresholds (e.g., 80%) in CI/CD