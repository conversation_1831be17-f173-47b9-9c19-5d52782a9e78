# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/mcp_server?schema=public"

# Server Configuration
PORT=3000
NODE_ENV=development

# Proxy Configuration (optional)
PROXY_ROTATION_ENABLED=false
PROXY_LIST_URL=""

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Amazon Scraping Configuration
AMAZON_BASE_URL=https://www.amazon.sg
USER_AGENT_ROTATION_ENABLED=true

# Image Processing Configuration
MAX_IMAGE_SIZE_MB=10
IMAGE_TIMEOUT_MS=30000

# Content Processing Configuration
MAX_CONTENT_TOKENS=4000
CONTENT_TRUNCATION_ENABLED=true
