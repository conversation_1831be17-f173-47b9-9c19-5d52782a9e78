import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
export declare const searchRequestSchema: z.ZodObject<{
    topic: z.ZodString;
    limit: z.ZodDefault<z.ZodOptional<z.ZodNumber>>;
}, z.core.$strip>;
export declare const productDetailsRequestSchema: z.ZodObject<{
    asin: z.ZodString;
}, z.core.$strip>;
export declare const fetchImageRequestSchema: z.ZodObject<{
    image_url: z.ZodString;
}, z.core.$strip>;
export declare const validateRequest: (schema: z.ZodSchema) => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateSearchRequest: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateProductDetailsRequest: (req: Request, res: Response, next: NextFunction) => void;
export declare const validateFetchImageRequest: (req: Request, res: Response, next: NextFunction) => void;
