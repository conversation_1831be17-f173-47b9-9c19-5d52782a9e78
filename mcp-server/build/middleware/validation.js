"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateFetchImageRequest = exports.validateProductDetailsRequest = exports.validateSearchRequest = exports.validateRequest = exports.fetchImageRequestSchema = exports.productDetailsRequestSchema = exports.searchRequestSchema = void 0;
const zod_1 = require("zod");
exports.searchRequestSchema = zod_1.z.object({
    topic: zod_1.z.string().min(1, 'Topic is required').max(200, 'Topic must be less than 200 characters'),
    limit: zod_1.z.number().int().min(1).max(50).optional().default(10)
});
exports.productDetailsRequestSchema = zod_1.z.object({
    asin: zod_1.z.string().min(10, 'ASIN must be at least 10 characters').max(10, 'ASIN must be exactly 10 characters').regex(/^[A-Z0-9]+$/, 'ASIN must contain only uppercase letters and numbers')
});
exports.fetchImageRequestSchema = zod_1.z.object({
    image_url: zod_1.z.string().url('Must be a valid URL').refine((url) => {
        try {
            const parsedUrl = new URL(url);
            return parsedUrl.protocol === 'http:' || parsedUrl.protocol === 'https:';
        }
        catch {
            return false;
        }
    }, 'Must be a valid HTTP or HTTPS URL')
});
const validateRequest = (schema) => {
    return (req, res, next) => {
        try {
            const validatedData = schema.parse(req.body);
            req.body = validatedData;
            next();
        }
        catch (error) {
            if (error instanceof zod_1.z.ZodError) {
                const formattedErrors = error.issues.map((err) => ({
                    field: err.path.join('.'),
                    message: err.message,
                    received: err.received
                }));
                res.status(400).json({
                    error: 'Validation failed',
                    details: formattedErrors
                });
            }
            else {
                console.error('Unexpected validation error:', error);
                res.status(500).json({
                    error: 'Internal server error during validation'
                });
            }
        }
    };
};
exports.validateRequest = validateRequest;
exports.validateSearchRequest = (0, exports.validateRequest)(exports.searchRequestSchema);
exports.validateProductDetailsRequest = (0, exports.validateRequest)(exports.productDetailsRequestSchema);
exports.validateFetchImageRequest = (0, exports.validateRequest)(exports.fetchImageRequestSchema);
//# sourceMappingURL=validation.js.map