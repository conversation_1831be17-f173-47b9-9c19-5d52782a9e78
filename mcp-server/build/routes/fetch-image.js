"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const fetch_image_1 = __importDefault(require("../controllers/fetch-image"));
const validation_1 = require("../middleware/validation");
const router = (0, express_1.Router)();
router.post('/', validation_1.validateFetchImageRequest, fetch_image_1.default.fetchImage);
exports.default = router;
//# sourceMappingURL=fetch-image.js.map