"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const product_details_1 = __importDefault(require("../controllers/product-details"));
const validation_1 = require("../middleware/validation");
const router = (0, express_1.Router)();
router.post('/', validation_1.validateProductDetailsRequest, product_details_1.default.getProductDetails);
exports.default = router;
//# sourceMappingURL=product-details.js.map