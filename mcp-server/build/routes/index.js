"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const search_1 = __importDefault(require("./search"));
const product_details_1 = __importDefault(require("./product-details"));
const fetch_image_1 = __importDefault(require("./fetch-image"));
const router = (0, express_1.Router)();
router.use("/search", search_1.default);
router.use("/product-details", product_details_1.default);
router.use("/fetch-image", fetch_image_1.default);
exports.default = router;
//# sourceMappingURL=index.js.map