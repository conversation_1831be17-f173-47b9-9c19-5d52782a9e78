export interface SearchRequest {
    topic: string;
    limit?: number;
}
export interface ProductDetailsRequest {
    asin: string;
}
export interface FetchImageRequest {
    image_url: string;
}
export interface ApiResponse<T> {
    data: T;
    error?: string;
}
export interface SearchResponse {
    search_term: string;
    product_identifiers: {
        asin: string;
    }[];
}
export interface ProductDetails {
    asin: string;
    title: string;
    description?: string;
    content: string;
    price: number;
    currency: string;
    main_image_url: string;
    secondary_image_urls: string[];
}
