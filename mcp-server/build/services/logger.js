"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.logger = exports.LogLevel = void 0;
var LogLevel;
(function (LogLevel) {
    LogLevel["ERROR"] = "error";
    LogLevel["WARN"] = "warn";
    LogLevel["INFO"] = "info";
    LogLevel["DEBUG"] = "debug";
})(LogLevel || (exports.LogLevel = LogLevel = {}));
class Logger {
    constructor() {
        this.serviceName = 'mcp-server';
        const envLogLevel = process.env.LOG_LEVEL?.toLowerCase();
        this.logLevel = this.isValidLogLevel(envLogLevel) ? envLogLevel : LogLevel.INFO;
    }
    isValidLogLevel(level) {
        return level !== undefined && Object.values(LogLevel).includes(level);
    }
    shouldLog(level) {
        const levels = [LogLevel.ERROR, LogLevel.WARN, LogLevel.INFO, LogLevel.DEBUG];
        const currentLevelIndex = levels.indexOf(this.logLevel);
        const messageLevelIndex = levels.indexOf(level);
        return messageLevelIndex <= currentLevelIndex;
    }
    createLogEntry(level, message, data) {
        return {
            timestamp: new Date().toISOString(),
            level,
            message,
            service: this.serviceName,
            ...data
        };
    }
    output(logEntry) {
        if (!this.shouldLog(logEntry.level)) {
            return;
        }
        const logFormat = process.env.LOG_FORMAT?.toLowerCase();
        if (logFormat === 'json') {
            console.log(JSON.stringify(logEntry));
        }
        else {
            const { timestamp, level, message, service, ...rest } = logEntry;
            const restStr = Object.keys(rest).length > 0 ? ` ${JSON.stringify(rest)}` : '';
            console.log(`[${timestamp}] ${level.toUpperCase()} [${service}] ${message}${restStr}`);
        }
    }
    error(message, data) {
        this.output(this.createLogEntry(LogLevel.ERROR, message, data));
    }
    warn(message, data) {
        this.output(this.createLogEntry(LogLevel.WARN, message, data));
    }
    info(message, data) {
        this.output(this.createLogEntry(LogLevel.INFO, message, data));
    }
    debug(message, data) {
        this.output(this.createLogEntry(LogLevel.DEBUG, message, data));
    }
    logRequest(method, path, data) {
        this.info('HTTP Request', {
            method,
            path,
            ...data
        });
    }
    logResponse(method, path, statusCode, responseTime, data) {
        this.info('HTTP Response', {
            method,
            path,
            statusCode,
            responseTime,
            ...data
        });
    }
    logScrapingAttempt(data) {
        this.info('Scraping Attempt', data);
    }
    logScrapingSuccess(data) {
        this.info('Scraping Success', data);
    }
    logScrapingFailure(data) {
        this.error('Scraping Failure', data);
    }
    logProxyRotation(proxyUrl, data) {
        this.debug('Proxy Rotation', {
            proxyUrl,
            ...data
        });
    }
    logUserAgentRotation(userAgent, data) {
        this.debug('User Agent Rotation', {
            userAgent,
            ...data
        });
    }
    logDatabaseOperation(operation, table, data) {
        this.debug('Database Operation', {
            operation,
            table,
            ...data
        });
    }
    logValidationError(endpoint, errors, data) {
        this.warn('Validation Error', {
            endpoint,
            errors,
            ...data
        });
    }
}
exports.logger = new Logger();
exports.default = exports.logger;
//# sourceMappingURL=logger.js.map