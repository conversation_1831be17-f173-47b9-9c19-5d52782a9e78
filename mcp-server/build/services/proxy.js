"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.proxyService = void 0;
const prisma_1 = __importDefault(require("../lib/prisma"));
class ProxyService {
    constructor() {
        this.proxies = [];
        this.currentIndex = 0;
        this.maxFailures = 3;
        this.cooldownPeriod = 5 * 60 * 1000;
        this.loadProxiesFromDatabase();
    }
    async loadProxiesFromDatabase() {
        try {
            const dbProxies = await prisma_1.default.proxy.findMany({
                where: { isActive: true }
            });
            this.proxies = dbProxies.map(proxy => ({
                url: proxy.url,
                isActive: proxy.isActive,
                lastUsed: proxy.lastUsed || undefined,
                failureCount: proxy.failureCount
            }));
            console.log(`Loaded ${this.proxies.length} proxies from database`);
        }
        catch (error) {
            console.error('Error loading proxies from database:', error);
            this.proxies = [];
        }
    }
    async addProxy(proxyUrl) {
        try {
            new URL(proxyUrl);
            await prisma_1.default.proxy.create({
                data: {
                    url: proxyUrl,
                    isActive: true,
                    failureCount: 0
                }
            });
            this.proxies.push({
                url: proxyUrl,
                isActive: true,
                failureCount: 0
            });
            console.log(`Added new proxy: ${proxyUrl}`);
        }
        catch (error) {
            console.error('Error adding proxy:', error);
            throw new Error('Invalid proxy URL or database error');
        }
    }
    async getNextProxy() {
        if (this.proxies.length === 0) {
            await this.loadProxiesFromDatabase();
            if (this.proxies.length === 0) {
                return null;
            }
        }
        const availableProxies = this.proxies.filter(proxy => proxy.isActive &&
            proxy.failureCount < this.maxFailures &&
            (!proxy.lastUsed || Date.now() - proxy.lastUsed.getTime() > this.cooldownPeriod));
        if (availableProxies.length === 0) {
            console.warn('No available proxies found');
            return null;
        }
        const proxy = availableProxies[this.currentIndex % availableProxies.length];
        this.currentIndex = (this.currentIndex + 1) % availableProxies.length;
        await this.updateProxyUsage(proxy.url);
        return proxy.url;
    }
    async reportProxyFailure(proxyUrl) {
        try {
            await prisma_1.default.proxy.update({
                where: { url: proxyUrl },
                data: {
                    failureCount: { increment: 1 }
                }
            });
            const proxy = this.proxies.find(p => p.url === proxyUrl);
            if (proxy) {
                proxy.failureCount++;
                if (proxy.failureCount >= this.maxFailures) {
                    proxy.isActive = false;
                    await prisma_1.default.proxy.update({
                        where: { url: proxyUrl },
                        data: { isActive: false }
                    });
                    console.warn(`Deactivated proxy due to failures: ${proxyUrl}`);
                }
            }
        }
        catch (error) {
            console.error('Error reporting proxy failure:', error);
        }
    }
    async updateProxyUsage(proxyUrl) {
        try {
            await prisma_1.default.proxy.update({
                where: { url: proxyUrl },
                data: { lastUsed: new Date() }
            });
            const proxy = this.proxies.find(p => p.url === proxyUrl);
            if (proxy) {
                proxy.lastUsed = new Date();
            }
        }
        catch (error) {
            console.error('Error updating proxy usage:', error);
        }
    }
    async getProxyConfig() {
        const proxyUrl = await this.getNextProxy();
        if (!proxyUrl) {
            return null;
        }
        try {
            const url = new URL(proxyUrl);
            return {
                host: url.hostname,
                port: parseInt(url.port) || (url.protocol === 'https:' ? 443 : 80),
                protocol: url.protocol.replace(':', ''),
                auth: url.username && url.password ? {
                    username: url.username,
                    password: url.password
                } : undefined
            };
        }
        catch (error) {
            console.error('Error parsing proxy URL:', error);
            return null;
        }
    }
    isEnabled() {
        return process.env.PROXY_ROTATION_ENABLED === 'true' && this.proxies.length > 0;
    }
    getStats() {
        const total = this.proxies.length;
        const active = this.proxies.filter(p => p.isActive && p.failureCount < this.maxFailures).length;
        const failed = this.proxies.filter(p => !p.isActive || p.failureCount >= this.maxFailures).length;
        return { total, active, failed };
    }
}
exports.proxyService = new ProxyService();
exports.default = exports.proxyService;
//# sourceMappingURL=proxy.js.map