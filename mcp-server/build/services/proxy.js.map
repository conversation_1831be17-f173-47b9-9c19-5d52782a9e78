{"version": 3, "file": "proxy.js", "sourceRoot": "", "sources": ["../../src/services/proxy.ts"], "names": [], "mappings": ";;;;;;AAAA,2DAAmC;AAanC,MAAM,YAAY;IAMhB;QALQ,YAAO,GAAkB,EAAE,CAAC;QAC5B,iBAAY,GAAW,CAAC,CAAC;QACzB,gBAAW,GAAW,CAAC,CAAC;QACxB,mBAAc,GAAW,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;QAG7C,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACjC,CAAC;IAKO,KAAK,CAAC,uBAAuB;QACnC,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,gBAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;gBAC5C,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE;aAC1B,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;gBACrC,GAAG,EAAE,KAAK,CAAC,GAAG;gBACd,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,SAAS;gBACrC,YAAY,EAAE,KAAK,CAAC,YAAY;aACjC,CAAC,CAAC,CAAC;YAEJ,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,MAAM,wBAAwB,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;YAC7D,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;QACpB,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,QAAQ,CAAC,QAAgB;QAC7B,IAAI,CAAC;YAEH,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;YAGlB,MAAM,gBAAM,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,IAAI,EAAE;oBACJ,GAAG,EAAE,QAAQ;oBACb,QAAQ,EAAE,IAAI;oBACd,YAAY,EAAE,CAAC;iBAChB;aACF,CAAC,CAAC;YAGH,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBAChB,GAAG,EAAE,QAAQ;gBACb,QAAQ,EAAE,IAAI;gBACd,YAAY,EAAE,CAAC;aAChB,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,oBAAoB,QAAQ,EAAE,CAAC,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;YAC5C,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY;QAChB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACrC,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAGD,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACnD,KAAK,CAAC,QAAQ;YACd,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW;YACrC,CAAC,CAAC,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,QAAQ,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,cAAc,CAAC,CACjF,CAAC;QAEF,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAGD,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAC5E,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,gBAAgB,CAAC,MAAM,CAAC;QAGtE,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEvC,OAAO,KAAK,CAAC,GAAG,CAAC;IACnB,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,QAAgB;QACvC,IAAI,CAAC;YAEH,MAAM,gBAAM,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE;gBACxB,IAAI,EAAE;oBACJ,YAAY,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE;iBAC/B;aACF,CAAC,CAAC;YAGH,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC;YACzD,IAAI,KAAK,EAAE,CAAC;gBACV,KAAK,CAAC,YAAY,EAAE,CAAC;gBAGrB,IAAI,KAAK,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;oBAC3C,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;oBACvB,MAAM,gBAAM,CAAC,KAAK,CAAC,MAAM,CAAC;wBACxB,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE;wBACxB,IAAI,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE;qBAC1B,CAAC,CAAC;oBACH,OAAO,CAAC,IAAI,CAAC,sCAAsC,QAAQ,EAAE,CAAC,CAAC;gBACjE,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAKO,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QAC7C,IAAI,CAAC;YACH,MAAM,gBAAM,CAAC,KAAK,CAAC,MAAM,CAAC;gBACxB,KAAK,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE;gBACxB,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,IAAI,EAAE,EAAE;aAC/B,CAAC,CAAC;YAGH,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC;YACzD,IAAI,KAAK,EAAE,CAAC;gBACV,KAAK,CAAC,QAAQ,GAAG,IAAI,IAAI,EAAE,CAAC;YAC9B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc;QAClB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;QAE3C,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC;YAC9B,OAAO;gBACL,IAAI,EAAE,GAAG,CAAC,QAAQ;gBAClB,IAAI,EAAE,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClE,QAAQ,EAAE,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;gBACvC,IAAI,EAAE,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;oBACnC,QAAQ,EAAE,GAAG,CAAC,QAAQ;oBACtB,QAAQ,EAAE,GAAG,CAAC,QAAQ;iBACvB,CAAC,CAAC,CAAC,SAAS;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAKD,SAAS;QACP,OAAO,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,MAAM,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;IAClF,CAAC;IAKD,QAAQ;QACN,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QAClC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC;QAChG,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,CAAC;QAElG,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;IACnC,CAAC;CACF;AAGY,QAAA,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;AAC/C,kBAAe,oBAAY,CAAC"}