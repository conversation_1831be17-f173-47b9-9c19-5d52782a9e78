export declare enum LogLevel {
    ERROR = "error",
    WARN = "warn",
    INFO = "info",
    DEBUG = "debug"
}
interface ScrapingLogData {
    asin?: string;
    topic?: string;
    url?: string;
    statusCode?: number;
    userAgent?: string;
    proxyUrl?: string;
    responseTime?: number;
    error?: string;
}
declare class Logger {
    private serviceName;
    private logLevel;
    constructor();
    private isValidLogLevel;
    private shouldLog;
    private createLogEntry;
    private output;
    error(message: string, data?: Record<string, any>): void;
    warn(message: string, data?: Record<string, any>): void;
    info(message: string, data?: Record<string, any>): void;
    debug(message: string, data?: Record<string, any>): void;
    logRequest(method: string, path: string, data?: Record<string, any>): void;
    logResponse(method: string, path: string, statusCode: number, responseTime: number, data?: Record<string, any>): void;
    logScrapingAttempt(data: ScrapingLogData): void;
    logScrapingSuccess(data: ScrapingLogData): void;
    logScrapingFailure(data: ScrapingLogData): void;
    logProxyRotation(proxyUrl: string, data?: Record<string, any>): void;
    logUserAgentRotation(userAgent: string, data?: Record<string, any>): void;
    logDatabaseOperation(operation: string, table: string, data?: Record<string, any>): void;
    logValidationError(endpoint: string, errors: any[], data?: Record<string, any>): void;
}
export declare const logger: Logger;
export default logger;
