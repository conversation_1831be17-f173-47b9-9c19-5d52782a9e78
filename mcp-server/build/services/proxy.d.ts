declare class ProxyService {
    private proxies;
    private currentIndex;
    private maxFailures;
    private cooldownPeriod;
    constructor();
    private loadProxiesFromDatabase;
    addProxy(proxyUrl: string): Promise<void>;
    getNextProxy(): Promise<string | null>;
    reportProxyFailure(proxyUrl: string): Promise<void>;
    private updateProxyUsage;
    getProxyConfig(): Promise<any | null>;
    isEnabled(): boolean;
    getStats(): {
        total: number;
        active: number;
        failed: number;
    };
}
export declare const proxyService: ProxyService;
export default proxyService;
