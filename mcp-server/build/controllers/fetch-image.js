"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fetchImage = void 0;
const axios_1 = __importDefault(require("axios"));
const user_agent_1 = __importDefault(require("../services/user-agent"));
const proxy_1 = __importDefault(require("../services/proxy"));
const fetchImage = async (req, res) => {
    try {
        const { image_url } = req.body;
        if (!image_url) {
            res.status(400).json({
                error: 'image_url is required for image fetching'
            });
            return;
        }
        try {
            new URL(image_url);
        }
        catch (urlError) {
            res.status(400).json({
                error: 'Invalid image_url format'
            });
            return;
        }
        const userAgent = user_agent_1.default.getRotatedUserAgent();
        const proxyConfig = proxy_1.default.isEnabled() ? await proxy_1.default.getProxyConfig() : null;
        const axiosConfig = {
            responseType: 'stream',
            headers: {
                'User-Agent': userAgent
            },
            timeout: 30000
        };
        if (proxyConfig) {
            axiosConfig.proxy = proxyConfig;
        }
        const response = await axios_1.default.get(image_url, axiosConfig);
        const contentType = response.headers['content-type'] || 'application/octet-stream';
        if (!contentType.startsWith('image/')) {
            res.status(400).json({
                error: 'URL does not point to an image resource'
            });
            return;
        }
        res.setHeader('Content-Type', contentType);
        if (response.headers['content-length']) {
            res.setHeader('Content-Length', response.headers['content-length']);
        }
        response.data.pipe(res);
    }
    catch (error) {
        console.error('Error fetching image:', error);
        if (error.response && error.response.status === 404) {
            res.status(404).json({
                error: 'Image not found at the specified URL'
            });
            return;
        }
        else if (error.response && error.response.status === 403) {
            res.status(403).json({
                error: 'Access denied to the image resource'
            });
            return;
        }
        else if (error.code === 'ECONNREFUSED') {
            res.status(503).json({
                error: 'Unable to connect to the image server'
            });
            return;
        }
        else if (error.code === 'ENOTFOUND') {
            res.status(404).json({
                error: 'Image server not found'
            });
            return;
        }
        else if (error.code === 'ETIMEDOUT') {
            res.status(408).json({
                error: 'Request timeout while fetching image'
            });
            return;
        }
        res.status(500).json({
            error: 'Internal server error while fetching image'
        });
    }
};
exports.fetchImage = fetchImage;
exports.default = { fetchImage: exports.fetchImage };
//# sourceMappingURL=fetch-image.js.map