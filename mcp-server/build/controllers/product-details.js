"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProductDetails = void 0;
const axios_1 = __importDefault(require("axios"));
const cheerio_1 = __importDefault(require("cheerio"));
const prisma_1 = __importDefault(require("../lib/prisma"));
const user_agent_1 = __importDefault(require("../services/user-agent"));
const proxy_1 = __importDefault(require("../services/proxy"));
const AMAZON_BASE_URL = 'https://www.amazon.sg';
const getProductDetails = async (req, res) => {
    try {
        const { asin } = req.body;
        if (!asin) {
            res.status(400).json({
                error: 'ASIN is required for product details extraction'
            });
            return;
        }
        const productUrl = `${AMAZON_BASE_URL}/dp/${asin}`;
        const userAgent = user_agent_1.default.getRotatedUserAgent();
        const proxyConfig = proxy_1.default.isEnabled() ? await proxy_1.default.getProxyConfig() : null;
        const axiosConfig = {
            headers: {
                'User-Agent': userAgent
            },
            timeout: 30000
        };
        if (proxyConfig) {
            axiosConfig.proxy = proxyConfig;
        }
        const response = await axios_1.default.get(productUrl, axiosConfig);
        const $ = cheerio_1.default.load(response.data);
        let title = null;
        let description = null;
        let price = null;
        let currency = null;
        let mainImageUrl = null;
        const jsonLdScripts = $('script[type="application/ld+json"]');
        let jsonLdData = null;
        jsonLdScripts.each((_index, element) => {
            try {
                const scriptContent = $(element).html();
                if (scriptContent) {
                    const parsed = JSON.parse(scriptContent);
                    if (parsed['@type'] === 'Product' || (Array.isArray(parsed) && parsed.some((item) => item['@type'] === 'Product'))) {
                        const productData = Array.isArray(parsed) ? parsed.find((item) => item['@type'] === 'Product') : parsed;
                        jsonLdData = productData;
                        return false;
                    }
                }
            }
            catch (parseError) {
                console.error('Error parsing JSON-LD script:', parseError);
            }
            return true;
        });
        if (jsonLdData) {
            title = jsonLdData.name || null;
            description = jsonLdData.description || null;
            if (jsonLdData.offers) {
                const offers = Array.isArray(jsonLdData.offers) ? jsonLdData.offers : [jsonLdData.offers];
                const validOffer = offers.find((offer) => offer.availability === 'https://schema.org/InStock' ||
                    offer.availability === 'InStock');
                if (validOffer) {
                    price = parseFloat(validOffer.price);
                    currency = validOffer.priceCurrency || null;
                }
            }
            if (jsonLdData.image) {
                mainImageUrl = Array.isArray(jsonLdData.image) ? jsonLdData.image[0] : jsonLdData.image;
            }
        }
        if (!title) {
            const titleElement = $('span#productTitle, #title, #titleSection h1');
            title = titleElement.length > 0 ? titleElement.text().trim() : null;
        }
        if (!description) {
            const descriptionElement = $('#productDescription, #aplus, #feature-bullets, #productOverview');
            description = descriptionElement.length > 0 ? descriptionElement.text().trim() : null;
        }
        if (!price || !currency) {
            const priceElement = $('.a-price[data-a-size=xl], .a-price-whole, #priceblock_ourprice, #priceblock_dealprice');
            if (priceElement.length > 0) {
                const priceText = priceElement.text().replace(/[^[0-9]\.,]/g, '');
                price = parseFloat(priceText.replace(',', ''));
                if (priceElement.length > 0) {
                    const priceHtml = priceElement.html() || '';
                    if (priceHtml.includes('S$') || priceHtml.includes('SGD')) {
                        currency = 'SGD';
                    }
                    else if (priceHtml.includes('$') || priceHtml.includes('USD')) {
                        currency = 'USD';
                    }
                }
            }
        }
        if (!mainImageUrl) {
            const imgElement = $('#img-main, #landingImage, #imgTagWrapperId img, .a-dynamic-image');
            if (imgElement.length > 0) {
                mainImageUrl = imgElement.attr('src') || imgElement.attr('data-old-hires') || imgElement.attr('data-a-dynamic-image') || null;
                if (mainImageUrl && mainImageUrl.includes('{')) {
                    try {
                        const imageMap = JSON.parse(mainImageUrl.replace(/&quot;/g, '"'));
                        const sortedUrls = Object.keys(imageMap).sort((a, b) => {
                            const sizeA = imageMap[a];
                            const sizeB = imageMap[b];
                            return sizeB[0] * sizeB[1] - sizeA[0] * sizeA[1];
                        });
                        mainImageUrl = sortedUrls[0];
                    }
                    catch (jsonError) {
                        console.error('Error parsing dynamic image data:', jsonError);
                    }
                }
            }
        }
        const secondaryImageUrls = [];
        $('#altImages img, .imageThumbnail img').each((_index, element) => {
            const imgUrl = $(element).attr('src') || $(element).attr('data-old-hires');
            if (imgUrl && !secondaryImageUrls.includes(imgUrl)) {
                secondaryImageUrls.push(imgUrl);
            }
        });
        let content = '';
        const contentSelectors = [
            '#productDescription',
            '#aplus',
            '#feature-bullets',
            '#productOverview',
            '#productDetails',
            '.poTabular'
        ];
        contentSelectors.forEach(selector => {
            const element = $(selector);
            if (element.length > 0) {
                content += element.text().trim() + ' ';
            }
        });
        content = content.replace(/\s+/g, ' ').trim();
        if (content.length > 15000) {
            content = content.substring(0, 15000);
        }
        const productDetails = {
            asin,
            title: title || '',
            description: description || undefined,
            content,
            price: price || 0,
            currency: currency || 'SGD',
            main_image_url: mainImageUrl || '',
            secondary_image_urls: secondaryImageUrls
        };
        await prisma_1.default.product.upsert({
            where: { asin },
            update: {
                title,
                description,
                content,
                price,
                currency,
                mainImageUrl
            },
            create: {
                asin,
                title,
                description,
                content,
                price,
                currency,
                mainImageUrl
            }
        });
        const apiResponse = {
            data: productDetails
        };
        res.json(apiResponse);
    }
    catch (error) {
        console.error('Error fetching product details:', error);
        if (error.response && error.response.status === 404) {
            res.status(404).json({
                error: 'Product not found'
            });
            return;
        }
        else if (error.response && error.response.status === 429) {
            res.status(429).json({
                error: 'Too many requests. Please try again later'
            });
            return;
        }
        else if (error.code === 'ECONNREFUSED') {
            res.status(503).json({
                error: 'Unable to connect to Amazon. Service may be unavailable'
            });
            return;
        }
        res.status(500).json({
            error: 'Internal server error while fetching product details'
        });
    }
};
exports.getProductDetails = getProductDetails;
exports.default = { getProductDetails: exports.getProductDetails };
//# sourceMappingURL=product-details.js.map