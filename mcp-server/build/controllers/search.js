"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchProducts = void 0;
const axios_1 = __importDefault(require("axios"));
const cheerio_1 = __importDefault(require("cheerio"));
const prisma_1 = __importDefault(require("../lib/prisma"));
const user_agent_1 = __importDefault(require("../services/user-agent"));
const proxy_1 = __importDefault(require("../services/proxy"));
const logger_1 = __importDefault(require("../services/logger"));
const AMAZON_BASE_URL = 'https://www.amazon.sg';
const searchProducts = async (req, res) => {
    const startTime = Date.now();
    try {
        const { topic, limit = 10 } = req.body;
        logger_1.default.logScrapingAttempt({
            topic,
            url: `${AMAZON_BASE_URL}/s?k=${encodeURIComponent(topic)}`
        });
        if (!topic) {
            logger_1.default.logValidationError('/search', [{ field: 'topic', message: 'Topic is required' }]);
            res.status(400).json({
                error: 'Topic is required for product search'
            });
            return;
        }
        const searchUrl = `${AMAZON_BASE_URL}/s?k=${encodeURIComponent(topic)}`;
        const userAgent = user_agent_1.default.getRotatedUserAgent();
        const proxyConfig = proxy_1.default.isEnabled() ? await proxy_1.default.getProxyConfig() : null;
        const axiosConfig = {
            headers: {
                'User-Agent': userAgent
            },
            timeout: 30000
        };
        if (proxyConfig) {
            axiosConfig.proxy = proxyConfig;
        }
        const response = await axios_1.default.get(searchUrl, axiosConfig);
        const responseTime = Date.now() - startTime;
        logger_1.default.logScrapingSuccess({
            topic,
            url: searchUrl,
            statusCode: response.status,
            responseTime,
            userAgent,
            proxyUrl: proxyConfig?.host
        });
        const $ = cheerio_1.default.load(response.data);
        const productIdentifiers = [];
        $('[data-asin]').each((_index, element) => {
            const asin = $(element).attr('data-asin');
            if (asin && asin.trim() !== '' && productIdentifiers.length < limit) {
                productIdentifiers.push({ asin });
            }
        });
        const searchResponse = {
            search_term: topic,
            product_identifiers: productIdentifiers
        };
        const productPromises = productIdentifiers.map(({ asin }) => prisma_1.default.product.upsert({
            where: { asin },
            update: {},
            create: { asin }
        }));
        await Promise.all(productPromises);
        await prisma_1.default.searchQuery.create({
            data: {
                topic,
                limit,
                products: {
                    connect: productIdentifiers.map(({ asin }) => ({ asin }))
                }
            }
        });
        const apiResponse = {
            data: searchResponse
        };
        logger_1.default.info('Search completed successfully', {
            topic,
            productCount: productIdentifiers.length,
            responseTime: Date.now() - startTime
        });
        res.json(apiResponse);
    }
    catch (error) {
        const responseTime = Date.now() - startTime;
        logger_1.default.logScrapingFailure({
            topic: req.body.topic || 'unknown',
            url: `${AMAZON_BASE_URL}/s?k=${encodeURIComponent(req.body.topic || '')}`,
            error: error.message,
            responseTime,
            statusCode: error.response?.status
        });
        if (error.response && error.response.status === 404) {
            res.status(404).json({
                error: 'No products found for the given topic'
            });
            return;
        }
        else if (error.response && error.response.status === 429) {
            res.status(429).json({
                error: 'Too many requests. Please try again later'
            });
            return;
        }
        else if (error.code === 'ECONNREFUSED') {
            res.status(503).json({
                error: 'Unable to connect to Amazon. Service may be unavailable'
            });
            return;
        }
        res.status(500).json({
            error: 'Internal server error while searching for products'
        });
    }
};
exports.searchProducts = searchProducts;
exports.default = { searchProducts: exports.searchProducts };
//# sourceMappingURL=search.js.map