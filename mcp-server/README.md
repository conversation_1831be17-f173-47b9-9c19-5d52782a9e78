# MCP Server

A specialized microservice for interacting with Amazon.sg marketplace, providing clean APIs for product search, details extraction, and image fetching.

## Features

- **Product Search**: Search Amazon.sg for products by topic
- **Product Details**: Extract detailed product information including structured data
- **Image Fetching**: Download product images with proper content type handling
- **Proxy Rotation**: Support for rotating proxies to avoid IP bans
- **User Agent Rotation**: Automatic user agent rotation for better scraping reliability
- **Structured Logging**: JSON-formatted logs for monitoring and debugging
- **Type Safety**: Full TypeScript implementation with Zod validation
- **Database Integration**: PostgreSQL with Prisma ORM
- **Comprehensive Testing**: Unit, integration, and E2E tests
- **Docker Support**: Containerized deployment with Docker Compose

## API Endpoints

### POST /v1/search
Search for products on Amazon.sg by topic.

**Request:**
```json
{
  "topic": "best ergonomic office chairs",
  "limit": 10
}
```

**Response:**
```json
{
  "data": {
    "search_term": "best ergonomic office chairs",
    "product_identifiers": [
      {"asin": "B081H3Y55N"},
      {"asin": "B07Y8B1D3C"}
    ]
  }
}
```

### POST /v1/product-details
Get detailed information for a specific product.

**Request:**
```json
{
  "asin": "B081H3Y55N"
}
```

**Response:**
```json
{
  "data": {
    "asin": "B081H3Y55N",
    "title": "Herman Miller Aeron Chair",
    "description": "...",
    "content": "...",
    "price": 1599.00,
    "currency": "SGD",
    "main_image_url": "https://m.media-amazon.com/images/I/image1.jpg",
    "secondary_image_urls": ["https://m.media-amazon.com/images/I/image2.jpg"]
  }
}
```

### POST /v1/fetch-image
Download binary image data from a URL.

**Request:**
```json
{
  "image_url": "https://m.media-amazon.com/images/I/image1.jpg"
}
```

**Response:** Binary image data with appropriate Content-Type header.

## Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 15+
- Docker (optional)

### Local Development

1. **Clone and install dependencies:**
```bash
git clone <repository>
cd mcp-server
npm install
```

2. **Set up environment variables:**
```bash
cp .env.example .env
# Edit .env with your database URL and other settings
```

3. **Set up database:**
```bash
npm run prisma:migrate
npm run prisma:generate
```

4. **Start development server:**
```bash
npm run dev
```

### Docker Development

1. **Start with Docker Compose:**
```bash
npm run docker:dev
```

This will start:
- PostgreSQL database
- MCP Server in development mode with hot reload
- Accessible at http://localhost:3001

### Production Deployment

1. **Build and run with Docker:**
```bash
npm run docker:run
```

2. **Or build manually:**
```bash
npm run build
npm run prisma:migrate:deploy
npm start
```

## Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run tests for CI
npm run test:ci
```

## Environment Variables

See `.env.example` for all available configuration options:

- `DATABASE_URL`: PostgreSQL connection string
- `PORT`: Server port (default: 3000)
- `NODE_ENV`: Environment (development/production)
- `LOG_LEVEL`: Logging level (error/warn/info/debug)
- `PROXY_ROTATION_ENABLED`: Enable proxy rotation
- `USER_AGENT_ROTATION_ENABLED`: Enable user agent rotation

## Database Schema

The application uses Prisma ORM with the following models:

- **Product**: Stores scraped product data
- **SearchQuery**: Tracks search queries and results
- **Proxy**: Manages proxy rotation
- **ScrapingLog**: Logs scraping activities

## Architecture

- **Express.js**: Web framework with TypeScript
- **Prisma**: Type-safe database ORM
- **Zod**: Request validation and type inference
- **Cheerio**: HTML parsing and scraping
- **Axios**: HTTP client with proxy support
- **Jest**: Testing framework
- **Docker**: Containerization

## Monitoring and Logging

The server produces structured JSON logs for:
- HTTP requests and responses
- Scraping attempts and results
- Database operations
- Proxy rotation events
- Validation errors

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

[Add your license here]
