import { Request, Response, NextFunction } from 'express';
import { 
  validateSearchRequest, 
  validateProductDetailsRequest, 
  validateFetchImageRequest 
} from '../../../src/middleware/validation';

// Mock Express objects
const mockRequest = (body: any): Partial<Request> => ({
  body
});

const mockResponse = (): Partial<Response> => {
  const res: Partial<Response> = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  return res;
};

const mockNext: NextFunction = jest.fn();

describe('Validation Middleware', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateSearchRequest', () => {
    it('should pass validation with valid search request', () => {
      const req = mockRequest({ topic: 'test topic', limit: 5 });
      const res = mockResponse();

      validateSearchRequest(req as Request, res as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    it('should set default limit when not provided', () => {
      const req = mockRequest({ topic: 'test topic' });
      const res = mockResponse();

      validateSearchRequest(req as Request, res as Response, mockNext);

      expect(req.body.limit).toBe(10);
      expect(mockNext).toHaveBeenCalled();
    });

    it('should fail validation with empty topic', () => {
      const req = mockRequest({ topic: '' });
      const res = mockResponse();

      validateSearchRequest(req as Request, res as Response, mockNext);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Validation failed'
        })
      );
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should fail validation with invalid limit', () => {
      const req = mockRequest({ topic: 'test', limit: 100 });
      const res = mockResponse();

      validateSearchRequest(req as Request, res as Response, mockNext);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('validateProductDetailsRequest', () => {
    it('should pass validation with valid ASIN', () => {
      const req = mockRequest({ asin: 'B081H3Y55N' });
      const res = mockResponse();

      validateProductDetailsRequest(req as Request, res as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    it('should fail validation with invalid ASIN length', () => {
      const req = mockRequest({ asin: 'B081H3' });
      const res = mockResponse();

      validateProductDetailsRequest(req as Request, res as Response, mockNext);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should fail validation with invalid ASIN characters', () => {
      const req = mockRequest({ asin: 'b081h3y55n' });
      const res = mockResponse();

      validateProductDetailsRequest(req as Request, res as Response, mockNext);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });

  describe('validateFetchImageRequest', () => {
    it('should pass validation with valid HTTPS URL', () => {
      const req = mockRequest({ image_url: 'https://example.com/image.jpg' });
      const res = mockResponse();

      validateFetchImageRequest(req as Request, res as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    it('should pass validation with valid HTTP URL', () => {
      const req = mockRequest({ image_url: 'http://example.com/image.jpg' });
      const res = mockResponse();

      validateFetchImageRequest(req as Request, res as Response, mockNext);

      expect(mockNext).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    it('should fail validation with invalid URL', () => {
      const req = mockRequest({ image_url: 'not-a-url' });
      const res = mockResponse();

      validateFetchImageRequest(req as Request, res as Response, mockNext);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(mockNext).not.toHaveBeenCalled();
    });

    it('should fail validation with FTP URL', () => {
      const req = mockRequest({ image_url: 'ftp://example.com/image.jpg' });
      const res = mockResponse();

      validateFetchImageRequest(req as Request, res as Response, mockNext);

      expect(res.status).toHaveBeenCalledWith(400);
      expect(mockNext).not.toHaveBeenCalled();
    });
  });
});
