import userAgentService from '../../../src/services/user-agent';

describe('UserAgentService', () => {
  describe('getRandomUserAgent', () => {
    it('should return a valid user agent string', () => {
      const userAgent = userAgentService.getRandomUserAgent();
      expect(typeof userAgent).toBe('string');
      expect(userAgent.length).toBeGreaterThan(0);
      expect(userAgent).toContain('Mozilla');
    });

    it('should return different user agents on multiple calls', () => {
      const userAgents = new Set();
      for (let i = 0; i < 20; i++) {
        userAgents.add(userAgentService.getRandomUserAgent());
      }
      // Should have at least 2 different user agents in 20 calls
      expect(userAgents.size).toBeGreaterThan(1);
    });
  });

  describe('getNextUserAgent', () => {
    it('should return user agents in sequence', () => {
      const firstAgent = userAgentService.getNextUserAgent();
      const secondAgent = userAgentService.getNextUserAgent();
      
      expect(typeof firstAgent).toBe('string');
      expect(typeof secondAgent).toBe('string');
      expect(firstAgent).not.toBe(secondAgent);
    });

    it('should cycle through all user agents', () => {
      const totalAgents = userAgentService.getUserAgentCount();
      const seenAgents = new Set();
      
      // Get more user agents than available to test cycling
      for (let i = 0; i < totalAgents + 5; i++) {
        seenAgents.add(userAgentService.getNextUserAgent());
      }
      
      // Should have seen all available user agents
      expect(seenAgents.size).toBe(totalAgents);
    });
  });

  describe('getRotatedUserAgent', () => {
    it('should return a valid user agent string', () => {
      const userAgent = userAgentService.getRotatedUserAgent();
      expect(typeof userAgent).toBe('string');
      expect(userAgent.length).toBeGreaterThan(0);
    });
  });

  describe('getAllUserAgents', () => {
    it('should return an array of user agent strings', () => {
      const userAgents = userAgentService.getAllUserAgents();
      expect(Array.isArray(userAgents)).toBe(true);
      expect(userAgents.length).toBeGreaterThan(0);
      
      userAgents.forEach(agent => {
        expect(typeof agent).toBe('string');
        expect(agent.length).toBeGreaterThan(0);
      });
    });
  });

  describe('getUserAgentCount', () => {
    it('should return a positive number', () => {
      const count = userAgentService.getUserAgentCount();
      expect(typeof count).toBe('number');
      expect(count).toBeGreaterThan(0);
    });

    it('should match the length of getAllUserAgents', () => {
      const count = userAgentService.getUserAgentCount();
      const agents = userAgentService.getAllUserAgents();
      expect(count).toBe(agents.length);
    });
  });
});
