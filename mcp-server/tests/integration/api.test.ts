import request from 'supertest';
import app from '../../src/server';

// Mock external dependencies
jest.mock('axios');
jest.mock('../../src/lib/prisma', () => ({
  __esModule: true,
  default: {
    product: {
      upsert: jest.fn(),
      findMany: jest.fn(),
    },
    searchQuery: {
      create: jest.fn(),
    },
    proxy: {
      findMany: jest.fn().mockResolvedValue([]),
    },
  },
}));

describe('API Integration Tests', () => {
  describe('GET /', () => {
    it('should return health check information', async () => {
      const response = await request(app)
        .get('/')
        .expect(200);

      expect(response.body).toHaveProperty('message', 'MCP Server is running');
      expect(response.body).toHaveProperty('version');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('POST /v1/search', () => {
    it('should return 400 for missing topic', async () => {
      const response = await request(app)
        .post('/v1/search')
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
    });

    it('should return 400 for empty topic', async () => {
      const response = await request(app)
        .post('/v1/search')
        .send({ topic: '' })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
    });

    it('should return 400 for invalid limit', async () => {
      const response = await request(app)
        .post('/v1/search')
        .send({ topic: 'test', limit: 100 })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
    });
  });

  describe('POST /v1/product-details', () => {
    it('should return 400 for missing ASIN', async () => {
      const response = await request(app)
        .post('/v1/product-details')
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
    });

    it('should return 400 for invalid ASIN format', async () => {
      const response = await request(app)
        .post('/v1/product-details')
        .send({ asin: 'invalid' })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
    });
  });

  describe('POST /v1/fetch-image', () => {
    it('should return 400 for missing image_url', async () => {
      const response = await request(app)
        .post('/v1/fetch-image')
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
    });

    it('should return 400 for invalid URL format', async () => {
      const response = await request(app)
        .post('/v1/fetch-image')
        .send({ image_url: 'not-a-url' })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
    });
  });

  describe('404 Handler', () => {
    it('should return 404 for non-existent endpoints', async () => {
      const response = await request(app)
        .get('/non-existent')
        .expect(404);

      expect(response.body).toHaveProperty('error', 'Endpoint not found');
      expect(response.body).toHaveProperty('path', '/non-existent');
    });
  });
});
