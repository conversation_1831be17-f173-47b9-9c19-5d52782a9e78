# Requirement Document: Automated Content Generation Pipeline for Amazon.sg Products

## 1. Overview

This document outlines the requirements for an automated pipeline that generates content based on products from amazon.sg. The system will take a given topic, find relevant products, and generate various types of articles based on them.

## 2. Project Goals

- To automate the creation of product-related content.
- To create a scalable system for generating articles for different topics.
- To integrate with Amazon.sg for product information and MCP for web-related tasks.
- To efficiently store the generated content and associated assets.

## 2.1. Non-Goals (Out of Scope)

- This system will not handle user comments or social media interactions on the generated content.
- The pipeline will not be responsible for publishing the content directly to a live website. It only stores it in the database.
- Real-time price tracking of products is not a requirement for this version.
- The system will not support any e-commerce marketplace other than `amazon.sg`.

## 3. Functional Requirements

### 3.1. Input

- The pipeline will accept a single `topic` as a string input.

### 3.2. Core Logic (Behavior)

The pipeline will perform the following steps in order:

1.  **Product Search:**
    - Given a `topic`, the system will search for relevant products on `amazon.sg`.
    - This search should be performed using the MCP (My Content Pipeline) service.
    - The number of products to retrieve should be configurable.
    - **Error Handling:** If no products are found for a topic, the pipeline should log this event and terminate gracefully for that topic.

2.  **Product Information Extraction:**
    - For each product found, the system will extract key information.
    - **Required Fields:** The system must extract the following fields: `ASIN`, `title`, `description`, `content`, `price`, `currency`, `main_image_url`, and at least 2 `secondary_image_urls`.
    - Product images should be retrieved via MCP.
    - Web page content should be retrieved via MCP.
    - **Token Optimization:** To limit token usage, the system will first attempt to extract structured data (e.g., from JSON-LD or specific HTML elements). If that fails, it will fall back to scraping the raw text, but will truncate the content to the first 4000 tokens before sending it to the language model for summarization.

3.  **Content Generation:**
    - The system will generate three types of content:
        1.  **Individual Product Content:** An article or description for each individual product.
        2.  **Topical Content:** A general article about the input `topic`.
        3.  **Compilation Content:** A "round-up" or "listicle" style article that features all the selected products for the given topic.

    - **Templates:** The generation process should use predefined prompt templates for each content type to ensure consistency and quality.
### 3.3. Output

- The pipeline will produce the generated articles in a structured format (e.g., Markdown or HTML).
- The output will be stored in the database.

## 4. Non-Functional Requirements

- **Performance:** The pipeline should be able to generate content for a given topic in a timely manner.
    - **SLO:** The P95 latency for a pipeline run (for a topic with 10 products) should be under 5 minutes.
- **Scalability:** The system should be designed to handle an increasing number of topics and products.
- **Cost-Effectiveness:** The usage of services like MCP for web scraping should be optimized to minimize token consumption.
- **Reliability:** The pipeline should have robust error handling for cases like failed product searches, unavailable web pages, or issues with content generation.
- **Maintainability:** The codebase should be well-structured, documented, and easy to modify.

### 4.1. Error Handling and Retries

- **MCP Service:** API calls to MCP should implement an exponential backoff retry mechanism for transient errors (e.g., 5xx status codes). After 3 failed retries, the job for that specific product/topic should be marked as 'failed' and an alert should be triggered.
- **LLM Service:** Calls to the language model should also have a similar retry mechanism.
- **Data Validation:** Data retrieved from Amazon/MCP should be validated against the expected schema before being processed or stored.
## 5. System Architecture

### 5.1. Components

- **Orchestrator:** A central component that manages the overall pipeline flow.
- **MCP Client:** A module for interacting with the MCP service for searching, image grabbing, and web content fetching.
- **Content Generator:** A module that uses a language model to generate the articles.
- **Data Storage:** A PostgreSQL database and an S3 bucket.

### 5.2. Storage

- **PostgreSQL Database:**
    - Will store structured data like article text, topics, and metadata.
    - See section 6 for the proposed database schema.
- **S3 Bucket:**
    - Will store product images and other binary assets.
    - The database will store links (URLs) to the objects in the S3 bucket.

## 6. Database Schema

Below is a proposed schema for the PostgreSQL database.

### `topics` table
- `id` (SERIAL, PRIMARY KEY)
- `name` (VARCHAR, UNIQUE, NOT NULL)
- `created_at` (TIMESTAMP, DEFAULT NOW())

### `products` table
- `id` (SERIAL, PRIMARY KEY)
- `amazon_asin` (VARCHAR, UNIQUE, NOT NULL)
- `title` (VARCHAR, NOT NULL)
- `description` (TEXT)
- `price` (DECIMAL(10, 2)) -- More suitable for calculations
- `currency` (VARCHAR(3), NOT NULL) -- e.g., 'SGD'
- `created_at` (TIMESTAMP, DEFAULT NOW())
- `content` (TEXT) -- Store the generated content for the product here

### `articles` table
-- This table stores all generated content.
-- For 'topic' and 'compilation' articles, `product_id` is NULL.
-- For 'compilation' articles, the associated products are linked via the `article_products` join table.

-- Proposed ENUM type for article_type and status
-- CREATE TYPE article_type_enum AS ENUM ('individual', 'topic', 'compilation');
-- CREATE TYPE article_status_enum AS ENUM ('draft', 'generating', 'completed', 'failed');

- `id` (SERIAL, PRIMARY KEY)
- `topic_id` (INTEGER, FOREIGN KEY REFERENCES topics(id))
- `product_id` (INTEGER, FOREIGN KEY REFERENCES products(id), NULLABLE) -- Null if it's a topic or compilation article
- `article_type` (article_type_enum, NOT NULL) -- e.g., 'individual', 'topic', 'compilation'
- `status` (article_status_enum, NOT NULL, DEFAULT 'draft')
- `title` (VARCHAR, NOT NULL)
- `content` (TEXT, NOT NULL)
- `created_at` (TIMESTAMP, DEFAULT NOW())
- `updated_at` (TIMESTAMP, DEFAULT NOW())

### `images` table
- `id` (SERIAL, PRIMARY KEY)
- `product_id` (INTEGER, FOREIGN KEY REFERENCES products(id))
- `s3_url` (VARCHAR, NOT NULL)
- `alt_text` (VARCHAR)
- `created_at` (TIMESTAMP, DEFAULT NOW())

### `article_products` table (New)
-- This join table creates a many-to-many relationship between compilation articles and products.
- `id` (SERIAL, PRIMARY KEY)
- `article_id` (INTEGER, NOT NULL, FOREIGN KEY REFERENCES articles(id))
- `product_id` (INTEGER, NOT NULL, FOREIGN KEY REFERENCES products(id))
- `sort_order` (INTEGER, DEFAULT 0) -- To maintain the order of products in a listicle

## 7. Assumptions

- The MCP service has APIs for searching Amazon, fetching web content, and grabbing images.
- Access to a large language model for content generation is available.
- Credentials for AWS (S3, PostgreSQL) and MCP are securely managed.
